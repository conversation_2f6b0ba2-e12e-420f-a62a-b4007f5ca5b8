/**
 * Image Utilities
 * 
 * Utilities for handling image operations, including robust image URL handling
 * and deletion with fallback strategies for production environments.
 */

/**
 * Extract image ID from various URL patterns
 * Supports multiple patterns to handle different image storage systems
 * @deprecated This function is kept for backward compatibility but is no longer used
 * with the new API that accepts full image URLs
 */
export const extractImageIdFromUrl = (url: string): string | null => {
  if (!url || typeof url !== 'string') {
    console.warn('Invalid URL provided to extractImageIdFromUrl:', url);
    return null;
  }

  try {
    console.log('Extracting image ID from URL:', url);
    
    // Pattern 1: Direct API endpoint pattern /images/123 or /images/123.jpg
    const match1 = url.match(/\/images\/(\d+)(?:\.|$)/);
    if (match1 && match1[1]) {
      console.log('Pattern 1 match (API endpoint):', match1[1]);
      return match1[1];
    }

    // Pattern 2: Cloudinary pattern with product_productId_imageId_index
    const match2 = url.match(/product_\d+_(\d+)_\d+\./);
    if (match2 && match2[1]) {
      console.log('Pattern 2 match (Cloudinary timestamp):', match2[1]);
      return match2[1];
    }

    // Pattern 3: Generic image_123_ pattern
    const match3 = url.match(/image_(\d+)_/);
    if (match3 && match3[1]) {
      console.log('Pattern 3 match (generic image pattern):', match3[1]);
      return match3[1];
    }

    // Pattern 4: Last number in URL path
    const match4 = url.match(/\/(\d+)(?:[^\/]*)?$/);
    if (match4 && match4[1]) {
      console.log('Pattern 4 match (last number):', match4[1]);
      return match4[1];
    }

    // Pattern 5: Cloudinary public_id pattern
    const match5 = url.match(/\/v\d+\/[^\/]+\/[^\/]+_(\d+)_/);
    if (match5 && match5[1]) {
      console.log('Pattern 5 match (Cloudinary public_id):', match5[1]);
      return match5[1];
    }

    // Pattern 6: Extract any sequence of digits that might be an ID
    const match6 = url.match(/(\d{10,})/); // Look for long numbers (timestamps, etc.)
    if (match6 && match6[1]) {
      console.log('Pattern 6 match (long number sequence):', match6[1]);
      return match6[1];
    }

    console.warn('No pattern matched for URL:', url);
    
    // Fallback: use a consistent hash of the URL as ID
    const fallbackId = Math.abs(url.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0)).toString();
    
    console.log('Using fallback hash ID:', fallbackId);
    return fallbackId;
  } catch (error) {
    console.error('Failed to extract image ID from URL:', url, error);
    return null;
  }
};

/**
 * Attempt to delete an image with multiple fallback strategies
 * Updated to work with the new API that accepts image URLs directly
 */
export const deleteImageWithFallback = async (
  productId: string,
  imageUrl: string,
  deleteFunction: (productId: string, imageUrl: string) => Promise<any>
): Promise<{ success: boolean; imageUrl: string; error?: string }> => {
  if (!imageUrl) {
    return {
      success: false,
      imageUrl,
      error: 'Image URL is required'
    };
  }

  try {
    await deleteFunction(productId, imageUrl);
    return {
      success: true,
      imageUrl
    };
  } catch (error: any) {
    console.warn(`Failed to delete image with URL ${imageUrl}:`, error);
    
    // If it's a 404 error, the image might already be deleted
    if (error.message?.includes('not found') || error.message?.includes('404')) {
      console.log('Image appears to already be deleted, treating as success');
      return {
        success: true,
        imageUrl,
        error: 'Image already deleted'
      };
    }
    
    return {
      success: false,
      imageUrl,
      error: error.message || 'Unknown error'
    };
  }
};

/**
 * Batch delete images with detailed results
 * Updated to work with the new API that accepts image URLs directly
 */
export const batchDeleteImages = async (
  productId: string,
  imageUrls: string[],
  deleteFunction: (productId: string, imageUrl: string) => Promise<any>
): Promise<{
  successful: number;
  failed: number;
  results: Array<{
    url: string;
    success: boolean;
    error?: string;
  }>;
}> => {
  console.log(`Starting batch deletion of ${imageUrls.length} images for product ${productId}`);
  
  const results = await Promise.allSettled(
    imageUrls.map(async (url) => {
      const result = await deleteImageWithFallback(productId, url, deleteFunction);
      return {
        url,
        success: result.success,
        error: result.error
      };
    })
  );

  const processedResults = results.map(result =>
    result.status === 'fulfilled'
      ? result.value
      : { url: 'unknown', success: false, error: 'Promise rejected' }
  ).map(result => ({
    url: result.url,
    success: result.success,
    ...(result.error && { error: result.error })
  }));

  const successful = processedResults.filter(r => r.success).length;
  const failed = processedResults.length - successful;

  console.log(`Batch deletion complete: ${successful} successful, ${failed} failed`);
  
  if (failed > 0) {
    console.warn('Failed deletions:', processedResults.filter(r => !r.success));
  }

  return {
    successful,
    failed,
    results: processedResults
  };
};

/**
 * Validate image URL format
 */
export const isValidImageUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  try {
    new URL(url);
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(url) || url.includes('cloudinary.com');
  } catch {
    return false;
  }
};

/**
 * Get image display name from URL
 */
export const getImageDisplayName = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || 'image';
    return filename.split('.')[0] || 'image';
  } catch {
    return 'image';
  }
};
