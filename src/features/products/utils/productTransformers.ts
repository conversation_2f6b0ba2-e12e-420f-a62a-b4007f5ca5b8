/**
 * Product Data Transformers
 *
 * This file provides utilities for transforming data between backend and frontend formats.
 */

import type {
  Product,
  BackendProduct,
  ProductFormData,
  FrontendProductFormData,
  ProductQueryParams,
  ProductAttribute,
  ProductVariant,
  BackendProductAttribute,
  BackendProductVariant,
  ApiResponseWrapper,
  PaginationInfo
} from '../types';

/**
 * Transform backend product attribute to frontend format
 */
export const transformAttributeFromBackend = (backendAttr: BackendProductAttribute): ProductAttribute => ({
  id: backendAttr.id.toString(),
  name: backendAttr.key, // Map 'key' to 'name' for frontend
  value: backendAttr.value
});

/**
 * Transform frontend product attribute to backend format
 */
export const transformAttributeToBackend = (frontendAttr: ProductAttribute): { Key: string; Value: string } => ({
  Key: frontendAttr.name, // Use 'name' from frontend
  Value: frontendAttr.value
});

/**
 * Transform backend product variant to frontend format
 */
export const transformVariantFromBackend = (backendVariant: BackendProductVariant): ProductVariant => ({
  id: backendVariant.id.toString(),
  name: backendVariant.name,
  type: backendVariant.type,
  price: backendVariant.price,
  stock: backendVariant.stock
});

/**
 * Transform frontend product variant to backend format
 */
export const transformVariantToBackend = (frontendVariant: ProductVariant): { Name: string; Type: string; CustomPrice: number; Stock: number } => ({
  Name: frontendVariant.name,
  Type: frontendVariant.type || 'Standard',
  CustomPrice: frontendVariant.price,
  Stock: frontendVariant.stock
});

/**
 * Transform backend product to frontend format
 */
export const transformProductFromBackend = (backendProduct: BackendProduct): Product => ({
  id: backendProduct.id.toString(), // Convert number to string for frontend
  name: backendProduct.name,
  sku: backendProduct.sku,
  category: backendProduct.category.name, // Extract category name
  categoryId: backendProduct.categoryId,
  price: backendProduct.price,
  stock: backendProduct.stock,
  minimumStock: backendProduct.minimumStock,
  status: backendProduct.stock > 0 ? 'active' : 'out_of_stock', // Derive status from stock
  description: backendProduct.description,
  image: backendProduct.image,
  images: backendProduct.images,
  supplierId: backendProduct.supplierId,
  customerId: backendProduct.customerId,
  attributes: backendProduct.attributes?.map(transformAttributeFromBackend),
  variants: backendProduct.variants?.map(transformVariantFromBackend),
  reviews: backendProduct.reviews,
  createdAt: backendProduct.createdAt,
  updatedAt: backendProduct.updatedAt
});

/**
 * Transform frontend form data to backend format
 * Note: SKU is auto-generated by backend, so we don't send it
 */
export const transformFrontendFormToBackend = (formData: FrontendProductFormData): ProductFormData => ({
  Name: formData.name,
  Description: formData.description || undefined,
  Price: formData.price,
  Stock: formData.stock,
  MinimumStock: formData.minimumStock,
  CategoryId: formData.categoryId || 1, // Default to category 1 if not provided
  SupplierId: formData.supplierId,
  CustomerId: formData.customerId || undefined,
  Attributes: formData.attributes?.map(attr => ({
    Key: attr.name, // Use 'name' from frontend
    Value: attr.value
  })),
  Variants: formData.variants?.map(variant => ({
    Name: variant.name,
    Type: variant.type || 'Standard',
    CustomPrice: variant.price,
    Stock: variant.stock
  }))
});

/**
 * Transform backend form data (already in correct format)
 */
export const transformProductFormToBackend = (formData: ProductFormData): ProductFormData => formData;

/**
 * Transform frontend query parameters to backend format
 */
export const transformQueryParamsToBackend = (params: ProductQueryParams): Record<string, any> => {
  const backendParams: Record<string, any> = {};

  if (params.page !== undefined) backendParams.page = params.page;
  if (params.limit !== undefined) backendParams.limit = params.limit;
  if (params.search !== undefined) backendParams.search = params.search;
  if (params.category !== undefined) backendParams.category = params.category; // Now expects number
  if (params.supplierId !== undefined) backendParams.supplierId = params.supplierId;
  if (params.inStock !== undefined) backendParams.inStock = params.inStock;
  if (params.sort !== undefined) backendParams.sort = params.sort;
  if (params.order !== undefined) backendParams.order = params.order;

  return backendParams;
};

/**
 * Transform backend products list response to frontend format
 */
export const transformProductsListResponse = (response: any): ApiResponseWrapper<Product[]> & { pagination?: PaginationInfo } => {
  // Handle direct array response (fallback)
  if (Array.isArray(response)) {
    return {
      success: true,
      message: 'Products retrieved successfully',
      data: response.map(transformProductFromBackend)
    };
  }

  // Handle wrapped response with pagination
  if (response.success !== undefined && response.data) {
    return {
      success: response.success,
      message: response.message || 'Products retrieved successfully',
      data: Array.isArray(response.data)
        ? response.data.map(transformProductFromBackend)
        : [],
      pagination: response.pagination // Include pagination info
    };
  }

  // Handle unexpected format
  throw new Error('Invalid response format from products API');
};

/**
 * Transform backend product response to frontend format
 */
export const transformProductResponse = (response: any): Product => {
  // Handle direct product response
  if (response.id || response.Id) {
    return transformProductFromBackend(response);
  }

  // Handle wrapped response
  if (response.success && response.data) {
    return transformProductFromBackend(response.data);
  }

  throw new Error('Invalid product response format');
};

/**
 * Validate backend response structure
 */
export const validateBackendResponse = <T>(response: any): ApiResponseWrapper<T> => {
  if (!response) {
    throw new Error('No response received from server');
  }

  if (response.success === false) {
    throw new Error(response.message || 'Request failed');
  }

  if (response.success === undefined) {
    // Handle direct data response (legacy)
    return {
      success: true,
      message: 'Request successful',
      data: response
    };
  }

  return response;
};

/**
 * Extract error message from backend response
 */
export const extractErrorMessage = (errorResponse: any): string => {
  if (typeof errorResponse === 'string') {
    return errorResponse;
  }

  if (errorResponse?.message) {
    return errorResponse.message;
  }

  if (errorResponse?.error) {
    return errorResponse.error;
  }

  if (errorResponse?.errors && Array.isArray(errorResponse.errors)) {
    return errorResponse.errors.join(', ');
  }

  return 'An unexpected error occurred';
};

/**
 * Validate frontend form data
 */
export const validateFrontendFormData = (formData: Partial<FrontendProductFormData>): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.name?.trim()) {
    errors.name = 'Product name is required';
  }

  // SKU is auto-generated by backend, so no validation needed

  if (!formData.category?.trim() && !formData.categoryId) {
    errors.category = 'Category is required';
  }

  if (formData.price === undefined || formData.price < 0) {
    errors.price = 'Valid price is required';
  }

  if (formData.stock === undefined || formData.stock < 0) {
    errors.stock = 'Valid stock quantity is required';
  }

  if (formData.minimumStock === undefined || formData.minimumStock < 0) {
    errors.minimumStock = 'Valid minimum stock is required';
  }

  if (formData.stock !== undefined && formData.minimumStock !== undefined && formData.stock < formData.minimumStock) {
    errors.stock = 'Stock cannot be less than minimum stock';
  }

  if (!formData.supplierId?.trim()) {
    errors.supplierId = 'Supplier is required';
  }

  // Validate GUID format for supplier ID
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (formData.supplierId && !guidRegex.test(formData.supplierId)) {
    errors.supplierId = 'Supplier ID must be a valid GUID format';
  }

  return errors;
};

/**
 * Validate product form data (backend format)
 */
export const validateProductFormData = (formData: Partial<ProductFormData>): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.Name?.trim()) {
    errors.Name = 'Product name is required';
  }

  if (!formData.CategoryId || formData.CategoryId <= 0) {
    errors.CategoryId = 'Category is required';
  }

  if (formData.Price === undefined || formData.Price < 0) {
    errors.Price = 'Valid price is required';
  }

  if (formData.Stock === undefined || formData.Stock < 0) {
    errors.Stock = 'Valid stock quantity is required';
  }

  if (formData.MinimumStock === undefined || formData.MinimumStock < 0) {
    errors.MinimumStock = 'Valid minimum stock is required';
  }

  if (formData.Stock !== undefined && formData.MinimumStock !== undefined && formData.Stock < formData.MinimumStock) {
    errors.Stock = 'Stock cannot be less than minimum stock';
  }

  if (!formData.SupplierId?.trim()) {
    errors.SupplierId = 'Supplier is required';
  }

  // Validate GUID format for supplier ID
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (formData.SupplierId && !guidRegex.test(formData.SupplierId)) {
    errors.SupplierId = 'Supplier ID must be a valid GUID format';
  }

  return errors;
};

// Export all transformers
export default {
  transformProductFromBackend,
  transformProductFormToBackend,
  transformFrontendFormToBackend,
  transformQueryParamsToBackend,
  transformProductsListResponse,
  transformProductResponse,
  validateBackendResponse,
  extractErrorMessage,
  validateProductFormData,
  validateFrontendFormData,
  transformAttributeFromBackend,
  transformAttributeToBackend,
  transformVariantFromBackend,
  transformVariantToBackend
};
